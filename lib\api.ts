// API service utilities for authentication and other endpoints
import axios from "axios";
import { encryptPassword, generateTimestampToken } from "./utils";
import { shouldUseMockData, mockApiResponses } from "./mock-data";

// Base API URL - use proxy for development, direct API for production
const getApiBaseUrl = () => {
  // Always use proxy when running in browser (client-side)
  if (typeof window !== "undefined") {
    // In production (Vercel), use direct API URL to avoid proxy issues
    if (process.env.NODE_ENV === "production") {
      const directApiUrl =
        process.env.NEXT_PUBLIC_PRODUCTION_API_URL ||
        "https://stockaty.virs.tech";

      return directApiUrl;
    }

    // In development, use the configured proxy URL
    if (process.env.NEXT_PUBLIC_API_BASE_URL) {
      return process.env.NEXT_PUBLIC_API_BASE_URL;
    }
  }

  // Fallback for server-side requests (shouldn't happen for auth)
  const fallbackUrl =
    process.env.NEXT_PUBLIC_PRODUCTION_API_URL ||
    process.env.NEXT_PUBLIC_API_URL ||
    "https://stockaty.virs.tech";

  return fallbackUrl;
};

const API_BASE_URL = getApiBaseUrl();

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  withCredentials: true, // Include cookies in requests for session management
  headers: {
    "Content-Type": "application/json",
    // Add CORS headers for production
    ...(process.env.NODE_ENV === "production" && {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-Access-Token",
    }),
  },
});

// Add request interceptor to include authorization token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage or sessionStorage
    const token =
      localStorage.getItem("access_token") ||
      sessionStorage.getItem("access_token");

    if (token) {
      // According to Swagger docs, API uses session cookies, but we'll also send token as header
      config.headers.Authorization = `Bearer ${token}`;
      // Also add the token as a custom header in case the backend expects it differently
      config.headers["X-Access-Token"] = token;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("API Response Error:", {
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
    });

    // Log detailed error information for debugging
    console.error("API request failed:", error);

    if (error.response) {
      console.error("Axios error details:", {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL,
        requestHeaders: error.config?.headers,
        requestData: error.config?.data,
      });

      // Log specific error details for 400 errors
      if (error.response.status === 400) {
        console.error("400 Bad Request Details:", {
          requestUrl: `${error.config?.baseURL}${error.config?.url}`,
          requestMethod: error.config?.method,
          requestHeaders: error.config?.headers,
          requestBody: error.config?.data,
          responseData: error.response.data,
          responseHeaders: error.response.headers,
        });

        // Try to parse and log the response data more clearly
        try {
          const responseData = error.response.data;
          console.error("Parsed API Error Response:", {
            success: responseData?.success,
            error: responseData?.error,
            message: responseData?.message,
            details: responseData?.details,
            validation: responseData?.validation,
            raw: responseData,
          });
        } catch (parseError) {
          console.error("Could not parse API error response:", parseError);
        }
      }
    } else if (error.request) {
      console.error("Network error - no response received:", error.request);
    } else {
      console.error("Request setup error:", error.message);
    }

    // If we get a 401 or authentication error, clear the token
    if (
      error.response?.status === 401 ||
      error.response?.status === 403 ||
      error.response?.data?.error?.message?.includes("Session is required") ||
      error.response?.data?.error?.message?.includes("Invalid token") ||
      error.response?.data?.error?.message?.includes("Unauthorized")
    ) {
      console.log("Authentication error detected, clearing tokens");
      localStorage.removeItem("access_token");
      sessionStorage.removeItem("access_token");

      // Optionally redirect to login page
      if (
        typeof window !== "undefined" &&
        window.location.pathname !== "/login" &&
        window.location.pathname !== "/register"
      ) {
        window.location.href = "/login";
      }
    }

    return Promise.reject(error);
  }
);

// API response types based on swagger documentation
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    id: number | string;
    message: string;
  };
}

export interface LoginResponse {
  access_token: string;
  email: string;
  message: string;
}

export interface RegisterResponse {
  access_token: string;
  email: string;
  message: string;
}

export interface UserAccount {
  email: string;
  picture?: string;
  firstName: string;
  lastName: string;
}

export interface Subscription {
  active: boolean;
  plan: string | null;
  credits: {
    remaining: number;
    plan: number;
  };
  until: string | null;
  allowed_sites: string[];
}

export interface UserData {
  account: UserAccount;
  subscription: Subscription;
  role?: string;
}

// Credit Analytics Types
export interface CreditStatistics {
  total_credits_issued: number;
  total_credits_used: number;
  total_remaining_credits: number;
  average_daily_usage: number;
  credits_by_plan: Record<string, number>;
  last_updated: string;
}

export interface CreditAnalyticsResponse {
  success: boolean;
  statistics: CreditStatistics;
}

// Credit History Types
export interface CreditHistoryEntry {
  id: number;
  user_email?: string; // Legacy field name
  email?: string; // New field name from API
  action: string;
  credits_changed: number;
  credits_before: number;
  credits_after: number;
  plan_name?: string;
  timestamp: string;
  description?: string;
}

export interface CreditHistoryResponse {
  success: boolean;
  history: CreditHistoryEntry[];
}

// Credit Management Request Types
export interface AddSubscriptionRequest {
  email: string;
  plan_name: string;
}

export interface UpgradeSubscriptionRequest {
  email: string;
  plan_name: string;
}

export interface ExtendSubscriptionRequest {
  email: string;
  days: number;
}

export interface DeleteSubscriptionRequest {
  email: string;
}

// Credit Management Response Types
export interface SubscriptionResponse {
  success: boolean;
  account?: UserAccount;
  subscription?: Subscription;
}

// User Management Types
export interface UsersStatistics {
  total_users: number;
  online_users: number;
  users: UserAccount[];
}

export interface UsersStatisticsResponse {
  success: boolean;
  total_users: number;
  online_users: number;
  users: UserAccount[];
}

export interface DownloadHistoryEntry {
  from: string;
  type: "photo" | "video" | "vector";
  price: number;
  date: string;
  file: string;
  downloadUrl?: string; // Optional field for the actual download link
}

export interface DownloadHistoryResponse {
  success: boolean;
  downloads: DownloadHistoryEntry[];
}

// Login request data
export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

// Register request data
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

// Generic API request function using axios
async function apiRequest<T>(
  endpoint: string,
  method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
  data?: Record<string, unknown>
): Promise<ApiResponse<T>> {
  try {
    const response = await apiClient.request({
      url: endpoint,
      method,
      data,
    });

    return response.data;
  } catch (error) {
    console.error("API request failed:", error);

    // Handle axios errors
    if (axios.isAxiosError(error)) {
      console.error("API Response Error:", {
        status: error.response?.status,
        data: error.response?.data,
        url: error.config?.url,
      });
      console.error("API request failed:", error);
      console.error("Parsed API Error Response:", error.response?.data);

      if (error.response?.data) {
        return error.response.data;
      }

      return {
        success: false,
        error: {
          id: error.code || "network_error",
          message: error.message || "Network error occurred. Please try again.",
        },
      };
    }

    return {
      success: false,
      error: {
        id: "unknown_error",
        message: "An unexpected error occurred. Please try again.",
      },
    };
  }
}

// Authentication API functions
export const authApi = {
  // Login user
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Auth API] Using mock login data");
      return mockApiResponses.login(credentials.email, credentials.password);
    }

    const encryptedPassword = encryptPassword(credentials.password);
    const token = generateTimestampToken();

    return apiRequest<LoginResponse>("/v1/auth/login", "POST", {
      email: credentials.email,
      password: encryptedPassword,
      token,
      remember_me: credentials.remember_me || false,
    });
  },

  // Register user
  async register(
    userData: RegisterRequest
  ): Promise<ApiResponse<RegisterResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Auth API] Using mock register data");
      return mockApiResponses.register(userData);
    }

    const encryptedPassword = encryptPassword(userData.password);
    const token = generateTimestampToken();

    console.log("[Register API] Request details:", {
      endpoint: "/v1/auth/register",
      baseURL: API_BASE_URL,
      fullURL: `${API_BASE_URL}/v1/auth/register`,
      payload: {
        email: userData.email,
        password: encryptedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        token,
      },
    });

    return apiRequest<RegisterResponse>("/v1/auth/register", "POST", {
      email: userData.email,
      password: encryptedPassword,
      firstName: userData.firstName,
      lastName: userData.lastName,
      token,
    });
  },

  // Logout user
  async logout(): Promise<
    ApiResponse<{ access_token: string; message: string }>
  > {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Auth API] Using mock logout data");
      return mockApiResponses.logout();
    }

    return apiRequest("/v1/auth/logout", "POST");
  },

  // Get current user data
  async getUserData(): Promise<ApiResponse<UserData>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Auth API] Using mock user data");
      return mockApiResponses.getUserData();
    }

    return apiRequest<UserData>("/v1/user/data", "GET");
  },
};

// User Management API functions
export const userApi = {
  // Get user data (line 481 from swagger)
  async getUserData(): Promise<ApiResponse<UserData>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[User API] Using mock user data");
      return mockApiResponses.getUserData();
    }

    return apiRequest<UserData>("/v1/user/data", "GET");
  },

  // Get users statistics (line 511 from swagger)
  async getUsersStatistics(): Promise<ApiResponse<UsersStatisticsResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[User API] Using mock users statistics data");
      return mockApiResponses.getUsersStatistics();
    }

    return apiRequest<UsersStatisticsResponse>("/v1/user/users", "GET");
  },

  // Get download history (line 547 from swagger)
  async getDownloadHistory(): Promise<ApiResponse<DownloadHistoryResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[User API] Using mock download history data");
      return mockApiResponses.getDownloadHistory();
    }

    return apiRequest<DownloadHistoryResponse>("/v1/user/history", "GET");
  },
};

// Credit Management API functions
export const creditApi = {
  // Add subscription (line 220 from swagger)
  async addSubscription(
    data: AddSubscriptionRequest
  ): Promise<ApiResponse<SubscriptionResponse>> {
    return apiRequest<SubscriptionResponse>("/v1/credit/subscribe", "POST", {
      email: data.email,
      plan_name: data.plan_name,
    });
  },

  // Upgrade subscription (line 266 from swagger)
  async upgradeSubscription(
    data: UpgradeSubscriptionRequest
  ): Promise<ApiResponse<SubscriptionResponse>> {
    return apiRequest<SubscriptionResponse>("/v1/credit/upgrade", "POST", {
      email: data.email,
      plan_name: data.plan_name,
    });
  },

  // Extend subscription (line 312 from swagger)
  async extendSubscription(
    data: ExtendSubscriptionRequest
  ): Promise<ApiResponse<SubscriptionResponse>> {
    return apiRequest<SubscriptionResponse>("/v1/credit/extend", "POST", {
      email: data.email,
      days: data.days,
    });
  },

  // Delete subscription (line 359 from swagger)
  async deleteSubscription(
    data: DeleteSubscriptionRequest
  ): Promise<ApiResponse<{ success: boolean }>> {
    return apiRequest<{ success: boolean }>("/v1/credit/delete", "POST", {
      email: data.email,
    });
  },

  // Get credit analytics (line 397 from swagger)
  async getCreditAnalytics(): Promise<ApiResponse<CreditAnalyticsResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Credit API] Using mock credit analytics data");
      return mockApiResponses.getCreditAnalytics();
    }

    return apiRequest<CreditAnalyticsResponse>("/v1/credit/analytics", "GET");
  },

  // Get credit history (line 450 from swagger)
  async getCreditHistory(): Promise<ApiResponse<CreditHistoryResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Credit API] Using mock credit history data");
      return mockApiResponses.getCreditHistory();
    }

    return apiRequest<CreditHistoryResponse>("/v1/credit/history", "GET");
  },
};

// Helper function to check if user is authenticated
export function isAuthenticated(): boolean {
  if (typeof window === "undefined") return false;

  // Check for access token in localStorage or sessionStorage
  const token =
    localStorage.getItem("access_token") ||
    sessionStorage.getItem("access_token");
  return !!token;
}

// Helper function to check if user has admin role
export function isAdmin(user: UserData | null): boolean {
  return user?.role === "admin";
}

// Helper function to test authorization
export async function testAuthorization(): Promise<boolean> {
  try {
    const response = await userApi.getUserData();
    return response.success;
  } catch (error) {
    console.error("Authorization test failed:", error);
    return false;
  }
}

// Helper function to store authentication token
export function storeAuthToken(
  token: string,
  rememberMe: boolean = false
): void {
  if (typeof window === "undefined") return;

  if (rememberMe) {
    localStorage.setItem("access_token", token);
  } else {
    sessionStorage.setItem("access_token", token);
  }
}

// Helper function to clear authentication token
export function clearAuthToken(): void {
  if (typeof window === "undefined") return;

  localStorage.removeItem("access_token");
  sessionStorage.removeItem("access_token");
}

// Site Management Types
export interface SiteInput {
  SiteName: string;
  SiteUrl: string;
  price?: string;
  icon?: string;
}

export interface SiteResponse {
  name: string;
  url: string;
  price: string;
}

export interface Site {
  name: string;
  url: string;
  icon?: string;
  total_downloads: number;
  today_downloads: number;
  price: number;
  last_reset: string;
}

export interface SitesResponse {
  success: boolean;
  data: {
    sites: Site[];
  };
}

export interface SiteActionResponse {
  success: boolean;
  data: {
    message: string;
    site: SiteResponse;
  };
}

export interface DeleteSiteRequest {
  SiteUrl: string;
}

export interface DeleteSiteResponse {
  success: boolean;
  data: {
    message: string;
    site: {
      Url: string;
    };
  };
}

// Site Management API functions
export const siteApi = {
  // Get all sites (line 868 from swagger)
  async getSites(): Promise<ApiResponse<SitesResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Site API] Using mock sites data");
      return mockApiResponses.getSites();
    }

    return apiRequest<SitesResponse>("/v1/sites/get", "GET");
  },

  // Add new site (line 737 from swagger)
  async addSite(data: SiteInput): Promise<ApiResponse<SiteActionResponse>> {
    return apiRequest<SiteActionResponse>("/v1/sites/add", "POST", {
      SiteName: data.SiteName,
      SiteUrl: data.SiteUrl,
      price: data.price || "1",
      icon: data.icon,
    });
  },

  // Edit existing site (line 777 from swagger)
  async editSite(data: SiteInput): Promise<ApiResponse<SiteActionResponse>> {
    return apiRequest<SiteActionResponse>("/v1/sites/edit", "POST", {
      SiteName: data.SiteName,
      SiteUrl: data.SiteUrl,
      price: data.price || "1",
      icon: data.icon,
    });
  },

  // Delete site (line 817 from swagger)
  async deleteSite(
    data: DeleteSiteRequest
  ): Promise<ApiResponse<DeleteSiteResponse>> {
    return apiRequest<DeleteSiteResponse>("/v1/sites/delete", "POST", {
      SiteUrl: data.SiteUrl,
    });
  },
};

// Pricing Management Types
export interface PricingPlanInput {
  PlanName: string;
  PlanPrice?: string;
  DaysValidity: string;
  Sites: string[]; // Backend expects array of site URLs
  PlanDescription: string;
  ContactUsUrl: string; // Required field
  credits: string;
}

export interface PricingPlan {
  id?: number;
  name: string;
  description: string;
  price?: string;
  credits: number;
  daysValidity: number;
  contactUsUrl?: string;
  supportedSites?: string[];
  features?: string[];
}

export interface PricingPlanResponse {
  success: boolean;
  message: string;
}

export interface GetPricingPlansResponse {
  success: boolean;
  data: PricingPlan[];
}

export interface DeletePricingPlanRequest {
  PlanName: string;
}

// Pricing Management API functions
export const pricingApi = {
  // Get all pricing plans (line 708 from swagger)
  async getPricingPlans(): Promise<ApiResponse<GetPricingPlansResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Pricing API] Using mock pricing plans data");
      return mockApiResponses.getPricingPlans();
    }

    return apiRequest<GetPricingPlansResponse>("/v1/pricing/get", "GET");
  },

  // Add new pricing plan (line 597 from swagger)
  async addPricingPlan(
    data: PricingPlanInput
  ): Promise<ApiResponse<PricingPlanResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Pricing API] Using mock add pricing plan data");
      return mockApiResponses.addPricingPlan(data);
    }

    const requestData = {
      PlanName: data.PlanName,
      PlanPrice: data.PlanPrice,
      DaysValidity: data.DaysValidity,
      Sites: data.Sites, // Send as array
      PlanDescription: data.PlanDescription,
      ContactUsUrl: data.ContactUsUrl, // Required field
      credits: data.credits,
    };

    console.log(
      "[Pricing API] Sending request to /v1/pricing/add with data:",
      requestData
    );

    return apiRequest<PricingPlanResponse>(
      "/v1/pricing/add",
      "POST",
      requestData
    );
  },

  // Edit existing pricing plan (line 632 from swagger)
  async editPricingPlan(
    data: PricingPlanInput
  ): Promise<ApiResponse<PricingPlanResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Pricing API] Using mock edit pricing plan data");
      return mockApiResponses.editPricingPlan(data);
    }

    return apiRequest<PricingPlanResponse>("/v1/pricing/edit", "POST", {
      PlanName: data.PlanName,
      PlanPrice: data.PlanPrice,
      DaysValidity: data.DaysValidity,
      Sites: data.Sites, // Send as array
      PlanDescription: data.PlanDescription,
      ContactUsUrl: data.ContactUsUrl, // Required field
      credits: data.credits,
    });
  },

  // Delete pricing plan (line 667 from swagger)
  async deletePricingPlan(
    data: DeletePricingPlanRequest
  ): Promise<ApiResponse<PricingPlanResponse>> {
    // Use mock data if enabled
    if (shouldUseMockData()) {
      console.log("[Pricing API] Using mock delete pricing plan data");
      return mockApiResponses.deletePricingPlan(data.PlanName);
    }

    return apiRequest<PricingPlanResponse>("/v1/pricing/delete", "POST", {
      PlanName: data.PlanName,
    });
  },
};
